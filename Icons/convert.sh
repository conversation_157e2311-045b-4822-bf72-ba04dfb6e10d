#!/bin/bash -e
ASSETS_PATH=../ZenTomato/Assets.xcassets
APPICON_SRC=ZenTomato.png
APPICON_ICONSET=${ASSETS_PATH}/AppIcon.appiconset
BARICON_SRC=tomato-filled.png
BARICON_ICONSET_IDLE=${ASSETS_PATH}/BarIconIdle.imageset

CONVERT="magick -verbose -background none"

if [ "$1" == "appicon" ]; then
    ${CONVERT} ${APPICON_SRC} -resize '!16x16' +repage ${APPICON_ICONSET}/icon_16x16.png
    ${CONVERT} ${APPICON_SRC} -resize '!32x32' +repage ${APPICON_ICONSET}/<EMAIL>
    ${CONVERT} ${APPICON_SRC} -resize '!32x32' +repage ${APPICON_ICONSET}/icon_32x32.png
    ${CONVERT} ${APPICON_SRC} -resize '!64x64' +repage ${APPICON_ICONSET}/<EMAIL>
    ${CONVERT} ${APPICON_SRC} -resize '!128x128' +repage ${APPICON_ICONSET}/icon_128x128.png
    ${CONVERT} ${APPICON_SRC} -resize '!256x256' +repage ${APPICON_ICONSET}/<EMAIL>
    ${CONVERT} ${APPICON_SRC} -resize '!256x256' +repage ${APPICON_ICONSET}/icon_256x256.png
    ${CONVERT} ${APPICON_SRC} -resize '!512x512' +repage ${APPICON_ICONSET}/<EMAIL>
    ${CONVERT} ${APPICON_SRC} -resize '!512x512' +repage ${APPICON_ICONSET}/icon_512x512.png
    ${CONVERT} ${APPICON_SRC} -resize '!1024x1024' +repage ${APPICON_ICONSET}/<EMAIL>
fi

if [ "$1" == "baricon" ]; then
    # 生成空闲状态的菜单栏图标（只有番茄图标，没有文字）
    for SCALE in $(seq 1 3); do
        IMAGE_SIZE="!$((16*SCALE))x$((16*SCALE))"
        SCALE_NAME="@${SCALE}x"
        if [ ${SCALE} -eq 1 ]; then
            SCALE_NAME=""
        fi
        DEST_NAME="${BARICON_ICONSET_IDLE}/icon_16x16${SCALE_NAME}.png"
        ${CONVERT} ${BARICON_SRC} -resize "${IMAGE_SIZE}" +repage ${DEST_NAME}
    done
fi
