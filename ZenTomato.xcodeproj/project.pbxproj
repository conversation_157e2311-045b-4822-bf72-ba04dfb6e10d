// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		08D10D9D2E4C7567004C44CE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08D10D862E4C7567004C44CE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 08D10D8D2E4C7567004C44CE;
			remoteInfo = ZenTomato;
		};
		08D10DA72E4C7568004C44CE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08D10D862E4C7567004C44CE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 08D10D8D2E4C7567004C44CE;
			remoteInfo = ZenTomato;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		08D10D8E2E4C7567004C44CE /* ZenTomato.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ZenTomato.app; sourceTree = BUILT_PRODUCTS_DIR; };
		08D10D9C2E4C7567004C44CE /* ZenTomatoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ZenTomatoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		08D10DA62E4C7568004C44CE /* ZenTomatoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ZenTomatoUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		08D10D902E4C7567004C44CE /* ZenTomato */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ZenTomato;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		08D10D8B2E4C7567004C44CE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		08D10D992E4C7567004C44CE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		08D10DA32E4C7568004C44CE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08D10D852E4C7567004C44CE = {
			isa = PBXGroup;
			children = (
				08D10D902E4C7567004C44CE /* ZenTomato */,
				08D10D8F2E4C7567004C44CE /* Products */,
			);
			sourceTree = "<group>";
		};
		08D10D8F2E4C7567004C44CE /* Products */ = {
			isa = PBXGroup;
			children = (
				08D10D8E2E4C7567004C44CE /* ZenTomato.app */,
				08D10D9C2E4C7567004C44CE /* ZenTomatoTests.xctest */,
				08D10DA62E4C7568004C44CE /* ZenTomatoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		08D10D8D2E4C7567004C44CE /* ZenTomato */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 08D10DB02E4C7568004C44CE /* Build configuration list for PBXNativeTarget "ZenTomato" */;
			buildPhases = (
				08D10D8A2E4C7567004C44CE /* Sources */,
				08D10D8B2E4C7567004C44CE /* Frameworks */,
				08D10D8C2E4C7567004C44CE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				08D10D902E4C7567004C44CE /* ZenTomato */,
			);
			name = ZenTomato;
			packageProductDependencies = (
			);
			productName = ZenTomato;
			productReference = 08D10D8E2E4C7567004C44CE /* ZenTomato.app */;
			productType = "com.apple.product-type.application";
		};
		08D10D9B2E4C7567004C44CE /* ZenTomatoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 08D10DB32E4C7568004C44CE /* Build configuration list for PBXNativeTarget "ZenTomatoTests" */;
			buildPhases = (
				08D10D982E4C7567004C44CE /* Sources */,
				08D10D992E4C7567004C44CE /* Frameworks */,
				08D10D9A2E4C7567004C44CE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				08D10D9E2E4C7567004C44CE /* PBXTargetDependency */,
			);
			name = ZenTomatoTests;
			packageProductDependencies = (
			);
			productName = ZenTomatoTests;
			productReference = 08D10D9C2E4C7567004C44CE /* ZenTomatoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		08D10DA52E4C7568004C44CE /* ZenTomatoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 08D10DB62E4C7568004C44CE /* Build configuration list for PBXNativeTarget "ZenTomatoUITests" */;
			buildPhases = (
				08D10DA22E4C7568004C44CE /* Sources */,
				08D10DA32E4C7568004C44CE /* Frameworks */,
				08D10DA42E4C7568004C44CE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				08D10DA82E4C7568004C44CE /* PBXTargetDependency */,
			);
			name = ZenTomatoUITests;
			packageProductDependencies = (
			);
			productName = ZenTomatoUITests;
			productReference = 08D10DA62E4C7568004C44CE /* ZenTomatoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08D10D862E4C7567004C44CE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					08D10D8D2E4C7567004C44CE = {
						CreatedOnToolsVersion = 16.4;
					};
					08D10D9B2E4C7567004C44CE = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 08D10D8D2E4C7567004C44CE;
					};
					08D10DA52E4C7568004C44CE = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 08D10D8D2E4C7567004C44CE;
					};
				};
			};
			buildConfigurationList = 08D10D892E4C7567004C44CE /* Build configuration list for PBXProject "ZenTomato" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 08D10D852E4C7567004C44CE;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 08D10D8F2E4C7567004C44CE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				08D10D8D2E4C7567004C44CE /* ZenTomato */,
				08D10D9B2E4C7567004C44CE /* ZenTomatoTests */,
				08D10DA52E4C7568004C44CE /* ZenTomatoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		08D10D8C2E4C7567004C44CE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		08D10D9A2E4C7567004C44CE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		08D10DA42E4C7568004C44CE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		08D10D8A2E4C7567004C44CE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		08D10D982E4C7567004C44CE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		08D10DA22E4C7568004C44CE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		08D10D9E2E4C7567004C44CE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 08D10D8D2E4C7567004C44CE /* ZenTomato */;
			targetProxy = 08D10D9D2E4C7567004C44CE /* PBXContainerItemProxy */;
		};
		08D10DA82E4C7568004C44CE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 08D10D8D2E4C7567004C44CE /* ZenTomato */;
			targetProxy = 08D10DA72E4C7568004C44CE /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		08D10DAE2E4C7568004C44CE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		08D10DAF2E4C7568004C44CE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		08D10DB12E4C7568004C44CE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ZenTomato/ZenTomato.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "禅番茄";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shuimuyi.ZenTomato;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		08D10DB22E4C7568004C44CE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ZenTomato/ZenTomato.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "禅番茄";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shuimuyi.ZenTomato;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		08D10DB42E4C7568004C44CE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shuimuyi.ZenTomatoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ZenTomato.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ZenTomato";
			};
			name = Debug;
		};
		08D10DB52E4C7568004C44CE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shuimuyi.ZenTomatoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ZenTomato.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ZenTomato";
			};
			name = Release;
		};
		08D10DB72E4C7568004C44CE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shuimuyi.ZenTomatoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = ZenTomato;
			};
			name = Debug;
		};
		08D10DB82E4C7568004C44CE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W4MWPBP23Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shuimuyi.ZenTomatoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = ZenTomato;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		08D10D892E4C7567004C44CE /* Build configuration list for PBXProject "ZenTomato" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				08D10DAE2E4C7568004C44CE /* Debug */,
				08D10DAF2E4C7568004C44CE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		08D10DB02E4C7568004C44CE /* Build configuration list for PBXNativeTarget "ZenTomato" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				08D10DB12E4C7568004C44CE /* Debug */,
				08D10DB22E4C7568004C44CE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		08D10DB32E4C7568004C44CE /* Build configuration list for PBXNativeTarget "ZenTomatoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				08D10DB42E4C7568004C44CE /* Debug */,
				08D10DB52E4C7568004C44CE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		08D10DB62E4C7568004C44CE /* Build configuration list for PBXNativeTarget "ZenTomatoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				08D10DB72E4C7568004C44CE /* Debug */,
				08D10DB82E4C7568004C44CE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08D10D862E4C7567004C44CE /* Project object */;
}
